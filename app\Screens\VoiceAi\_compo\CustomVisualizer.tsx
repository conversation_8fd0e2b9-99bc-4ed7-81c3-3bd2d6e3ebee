import React, { useEffect, useRef } from 'react';
import { Animated, View, Text } from 'react-native';

const MicCircle = ({ level = 0 }) => {
    const scale = useRef(new Animated.Value(1)).current;


    return (
        <>
            <Animated.View
                style={{
                    width: 70,
                    height: 70,
                    borderRadius: 35,
                    backgroundColor: level > 0.05 ? '#10b981' : '#d1d5db',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transform: [{ scale }],
                    shadowColor: '#000',
                    shadowOpacity: level > 0.05 ? 0.4 : 0,
                    shadowRadius: 10,
                }}
            />
            <Text>{level}</Text>
        </>
    );
};

export default MicCircle;
